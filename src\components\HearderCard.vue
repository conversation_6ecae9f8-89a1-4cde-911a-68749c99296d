<template>
  <div class="flex flex-wrap gap-4 flex-conter">
    <el-card>
      <div class="card-content">
        <div class="flex-item">1</div>
        <div class="flex-item">2</div>
        <div class="flex-item">3</div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
</script>

<style scoped>
.el-card {
  height: 100%;
  width: 100%;
  box-shadow: 0 0px 3px rgba(0, 0, 0, 0.1) !important; /* 自定义阴影 */
  border-radius: 0px !important;
}
:deep(.el-card__body)  {
  padding: 0 !important;
}

.card-content {
    display: flex;
    gap: 0px; 
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
.flex-item {
  flex: 1;
  padding: 10px;
  background: #f5f7fa;     /* 可选：背景色 */
  border-radius: 0px;      /* 可选：圆角 */
}
</style>