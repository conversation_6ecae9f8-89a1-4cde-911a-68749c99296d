<template>
  <div class="logo-search">
    <div class="logo">
        <a href="javascript:;">Music</a>
    </div>

    <div class="flex gap-4 items-center mystyle">
        <el-input
            v-model="input2"
            style="width: 200px;"
            placeholder="搜索"
            :prefix-icon="Search"
        />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const input2 = ref('')
</script>

<style scoped>

.mystyle {
  padding-top: 10px;
  text-align: center;
}

.logo-search {
    display: flex;
    flex-direction: column;
}
.logo-search .logo {
    font-size: 30px;
    font-weight: bold;
    
    padding-top: 15px;
    padding-bottom: 15px;

    text-align: center;
}
.logo-search .logo a {
    color: #000;
    text-decoration: none;
}

/* 移除聚焦时的边框颜色 */
:deep(.el-input__wrapper.is-focus) {
  box-shadow: none !important;
  /* border: none !important; */
}
</style>
