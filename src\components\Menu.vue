<template>

  <el-row class="tac">
    <el-col >
      <el-menu
        default-active="2"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
      >
        <Input />

        <div class="menu-item">
            <el-menu-item index="1">
                <el-icon style="color: red;"><House /></el-icon>
                <span>主页</span>
            </el-menu-item>

            <el-menu-item index="2">  <!-- 加 disabled 禁用 -->
                <el-icon style="color: red;"><Headset /></el-icon>
                <span>音乐馆</span>
            </el-menu-item>
        </div>

      </el-menu>
    </el-col>
  </el-row>
</template>


<script lang="ts" setup>
import {
  Headset,
  House,
} from '@element-plus/icons-vue'
import Input from './Input.vue'


const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script>

<style scoped>


.menu-item {
    display: flex;
    flex-direction: column;
    color: #000;  /* 字体颜色 */
    padding-top: 15px;
}

.el-menu-item {
    text-align: center;
    font-size: 15px;

}

.el-menu-vertical-demo {
  height: 100vh;
  background-color: #F9F9F9;
}

/* 选中项背景色 */
:deep(.el-menu-item.is-active) {
  background-color: #E6E6E7 !important;
  color: #000;
  font-weight: bold;
}

/* 鼠标悬停项背景色 */
:deep(.el-menu-item:hover) {
    background-color: transparent !important; /* 完全透明 */
}

</style>
