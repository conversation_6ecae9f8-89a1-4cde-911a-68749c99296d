
<template>

  <Login v-if="showIndex" />
  <Index v-else />
  <router-view/>

  <!-- App.vue是整个Vue项目展示页面内容的地方

    如果 希望 路由 对应的 视图 内容 显示到 浏览器中， 必须在 App.vue 中 提供 渲染出

    router-view标签作用就是将路由对应的视图 渲染的出口位置 -->

</template>

<script>
import { defineAsyncComponent } from 'vue';
import Index from './views/Index.vue';

export default {
  components: {
    Login: defineAsyncComponent(() => import('./views/Login.vue')),
    Index,
  },
  computed: {
    showIndex() {
      return this.$route.path == '/login';  // 非登录页才显示
    },
  },
}
</script>

<style>
  /* html {
    height: 100%;
  } */
  body {
    margin-top: 0;
  }

</style>
