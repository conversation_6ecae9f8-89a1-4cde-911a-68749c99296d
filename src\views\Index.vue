
<template>

    <div class="common-layout">
        <el-container class="all-container">

            <el-aside>
                <Menu />
            </el-aside>

            <el-container class="right-container">
                <el-header class="custom-header">
                    <HearderCard />
                </el-header>

                <el-main>
                    <Main />
                </el-main>
            </el-container>

        </el-container>
        
    </div>

</template>

<script>
import Menu from '../components/Menu.vue';
import Input from '../components/Input.vue';
import HearderCard from '../components/HearderCard.vue';
import Main from '../components/Main.vue';

export default {
  components: {
    Menu,
    Input,
    HearderCard,
    Main,
  },
  methods: {

  },
  // created() {
    
  // },
  computed: {
  },
};
// 
</script>

<style scoped>
.common-layout {
  margin: 0;
  padding: 0;
  height: 100vh;
}

.el-aside {
  margin: 0;
  padding: 0;
}

.el-header {
  height: 80px !important;  /* 增加高度 */
}

.custom-header {
  padding: 0 !important;
  margin: 0 !important;
  height: 60px !important;
  display: flex;
  flex-direction: column;
}

:deep(.el-header) {
  padding: 0 !important;
}

.el-main {
  margin: 0;
  padding-top: 25px;
  padding-left: 40px;
  padding-right: 40px;

  flex: 1;
}

.all-container {
  display: flex;
  flex-direction: row;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.right-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0;
  padding: 0;
  height: 100vh;
}
</style>