
<template>

    <div class="common-layout">
        <el-container>
        <!-- <el-aside width="200px">Aside</el-aside> -->
         <Menu />
        <el-container>
            <el-header>Header</el-header>
            <el-main>Main</el-main>
        </el-container>
        </el-container>
    </div>

</template>

<script>
import Menu from '../components/Menu.vue';
import Input from '../components/Input.vue';

export default {
  components: {
    Menu,
    Input,
  },
  methods: {

  },
  // created() {
    
  // },
  computed: {
  },
};
// 
</script>

<style>
  /* html {
    height: 100%;
  } */
  /* body {
    margin-top: 0;
  } */

</style>