
<template>

    <div class="common-layout">
        <el-container class="all-container">

            <el-aside>
                <Menu />
            </el-aside>

            <el-container class="right-container">
                <el-header>
                    <HearderCard />
                </el-header>
                <el-main>Main</el-main>
            </el-container>

        </el-container>
        
    </div>

</template>

<script>
import Menu from '../components/Menu.vue';
import Input from '../components/Input.vue';
import HearderCard from '../components/HearderCard.vue';

export default {
  components: {
    Menu,
    Input,
    HearderCard,
  },
  methods: {

  },
  // created() {
    
  // },
  computed: {
  },
};
// 
</script>

<style scoped>
.el-aside {

}
.el-header {
    height: 60px;
}
:deep(.el-header) {
  padding: 0 !important; /* 直接覆盖 padding */
}
.el-main {

}

.all-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
   
}
.right-container {
    display: flex;
    flex-direction: column;
    /* flex: 1; */
    width: 100%;  /* 填充剩余宽度 */
}

</style>